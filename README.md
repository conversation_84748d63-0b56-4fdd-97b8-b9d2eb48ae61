# 最高限价编审助手前端项目

## 项目简介

最高限价编审助手是一个专为采购限价编制设计的智能化系统，旨在简化采购限价编制流程，提高工作效率，让采购决策更加智能化。

## 主要功能

### 1. 发起询价
- **功能描述**: 使用标准化询价流程，从供应商库或外部询价，生成可追溯的电子记录
- **图标**: `/img/inquiry-icon.svg` (本地SVG图标)
- **路由**: `/xjzs/inquiry`
- **特点**: 标准化流程、可追溯记录

### 2. 智能编审
- **功能描述**: 流程化引导设计，提供多种计算方式，预设标准化选项，减少手动输入
- **图标**: `/img/review-icon.svg` (本地SVG图标)
- **路由**: `/xjzs/project-assistant`
- **特点**: 流程化引导、多种计算方式、标准化选项

### 3. 智能问答
- **功能描述**: 获取采购政策解读、法规咨询、历史案例参考，智能解答各类采购问题
- **图标**: `/img/qa-icon.svg` (本地SVG图标)
- **路由**: `/intelligent-qa`
- **特点**: 政策解读、法规咨询、案例参考

## 技术栈

- **前端框架**: Vue.js
- **UI组件库**: Element Plus
- **构建工具**: Vite
- **样式**: SCSS + Tailwind CSS
- **状态管理**: Vuex
- **路由**: Vue Router

## 项目结构

```
src/
├── api/                 # API接口
├── components/          # 公共组件
├── router/             # 路由配置
├── store/              # 状态管理
├── styles/             # 样式文件
├── utils/              # 工具函数
└── views/              # 页面组件
    └── wel/            # 欢迎页面
        └── index.vue   # 主页面

public/
└── img/                # 静态图片资源
    ├── inquiry-icon.svg    # 发起询价图标
    ├── review-icon.svg     # 智能编审图标
    └── qa-icon.svg         # 智能问答图标
```

## 最近更新

### 图标显示和布局优化完成 (2024)
- ✅ 将外部SVG图标链接替换为本地图标文件
- ✅ 修复了图标容器的大小和居中显示问题
- ✅ 统一所有图标容器的样式：64x64px (w-16 h-16) 固定大小
- ✅ 使用 `flex items-center justify-center` 实现图标完美居中
- ✅ 每个图标都有对应的主题色，保持视觉一致性
- ✅ 提高了页面加载速度和稳定性
- ✅ 避免了外部资源依赖问题

#### 技术解决方案：
- **布局问题**: 图标容器大小不固定，图标未居中显示，Tailwind CSS类不完整
- **解决方案**:
  - 创建自定义CSS类 `.icon-container` 和 `.icon-size`
  - 容器固定尺寸：64x64px，使用flexbox完美居中
  - 图标统一大小：28x28px
  - 避免依赖可能缺失的Tailwind CSS类
- **优势**: 所有图标容器大小一致，图标完美居中，样式完全可控

#### 修复详情：
1. **发起询价图标**: 蓝色主题 (#2563eb) - 对话框图标
2. **智能编审图标**: 绿色主题 (#059669) - 文档编辑图标
3. **智能问答图标**: 紫色主题 (#7c3aed) - 问号圆圈图标

## 开发说明

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本
```bash
npm run build
# 或
yarn build
```

## 图标使用说明

项目中的功能图标已本地化，位于 `public/img/` 目录下：

1. **inquiry-icon.svg**: 发起询价功能图标，蓝色主题
2. **review-icon.svg**: 智能编审功能图标，绿色主题  
3. **qa-icon.svg**: 智能问答功能图标，紫色主题

所有图标都是SVG格式，支持响应式设计和颜色主题。

## 注意事项

- 图标文件路径使用绝对路径 `/img/` 开头
- 图标支持CSS类控制颜色和大小
- 建议保持图标的语义化命名规范
