<template>
  <div class="step-content">
    <h3>生成报告</h3>
    <p class="step-description">查看并导出项目最高限价报告</p>

    <!-- 报告操作按钮 -->
    <div class="report-actions">
      <el-button type="primary" @click="saveReport" v-if="!isDetailMode">
         保存报告
      </el-button>
      <el-button type="success" @click="downloadReportPdf">
        <el-icon><Download /></el-icon> 下载报告
      </el-button>
    </div>

    <!-- 报告内容 -->
    <div class="report-container">
      <div class="report-header">
        <h2 class="report-title">{{ formData.projectName }}最高限价计算报告</h2>
        <div class="report-meta">
          <div class="report-id">报告编号：{{ reportId }}</div>
          <div class="report-date">生成日期：{{ currentDate }}</div>
          <div class="report-author">生成人：{{ userInfo.real_name }}</div>
        </div>
      </div>

      <!-- 项目基本信息 -->
      <div class="report-section">
        <h3 class="section-title">项目基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">项目名称</div>
            <div class="info-value">{{ formData.projectName }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">项目编号</div>
            <div class="info-value">{{ formData.projectCode }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">项目类型</div>
            <div class="info-value">{{ formData.projectType }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">计算方式</div>
            <div class="info-value">{{ getCalculationMethodName(formData.calculationMethod) }}</div>
          </div>
        </div>
      </div>

      <!-- 计算结果摘要 -->
      <div class="report-section">
        <h3 class="section-title">计算结果摘要</h3>
        <div class="price-summary">
          <div class="price-label">最高限价（元）</div>
          <div class="price-value">¥{{ formatPrice(calculatedPrice) }}</div>
          <div class="price-in-words">（人民币：{{ priceInWords }}）</div>
        </div>
      </div>

      <!-- 一、编制依据 -->
      <div class="report-section">
        <h3 class="section-title">一、编制依据</h3>
        <div class="section-content">
          <p v-if="formData.algorithmCategory === '培训类'">
            根据《中国烟草总公司关于印发培训费管理规定的通知》（中烟办〔2017〕78号）对培训费的收取规定制定最高限价。
          </p>
          <p v-else-if="formData.algorithmCategory === '工程咨询类'">
            依据《工程咨询行业管理办法》（国家发展改革委2017年第9号令）、《建设工程造价咨询服务计费指导意见》（中价协〔2018〕11号）等文件，结合本项目特点，制定本限价。
          </p>
          <p v-else>
            依据《广东省湛江市人民政府采购中心关于印发政府采购限价管理办法（试行）》（2020年12月），《广东省湛江市人民政府采购中心关于进一步规范政府采购活动的通知》（2019年5月）等文件，结合本项目特点，制定本限价。本次限价编制综合考虑了历史采购数据、市场价格水平、行业标准以及项目特殊要求等因素，采用{{ getCalculationMethodName(formData.calculationMethod) }}进行计算，确保限价的科学性、合理性和公正性。
          </p>
        </div>
      </div>

      <!-- 二、限价范围 -->
      <div class="report-section">
        <h3 class="section-title">二、限价类型</h3>
        <div class="section-content">
          <p v-if="formData.algorithmCategory === '培训类'">
            本限价包括：师资费、住宿费、伙食费、场地费、资料费、交通费等培训相关费用。
          </p>
          <p v-else-if="formData.algorithmCategory === '工程咨询类'">
            本限价包括：人工费、设备费、软件费、差旅费、资料费、会议费、专家评审费等工程咨询相关费用。
          </p>
          <p v-else>
            本限价包括：材料费、人工费、管理费、利润、税金等。
          </p>
        </div>
      </div>

      <!-- 三、计算方法 -->
      <div class="report-section">
        <h3 class="section-title">三、计算方法</h3>
        <div class="section-content">
          <div v-if="formData.algorithmCategory === '培训类'">
            <p>1、算法：根据《中国烟草总公司关于印发培训费管理规定的通知》（中烟办〔2017〕78号）对培训费的收取规定制定最高限价。</p>
            <p>2、计价标准：师资费-副高级职称-≤500元/学时；师资费-正高级职称-≤1000元/学时；师资费-院士/首席专家-≤1500元/学时；综合定额-三类培训（处级及以下） 550元/人·天。</p>
            <p>3、计算规则：</p>
            <p>线上：培训费用 = ∑（师资费×学时）</p>
            <p>线下：培训费用 = ∑（师资费标准×学时）+（人数×天数×550）</p>
          </div>
          <div v-else-if="formData.algorithmCategory === '工程咨询类'">
            <p>1、算法：根据《建设工程造价咨询服务计费指导意见》（中价协〔2018〕11号）的计费标准进行计算。</p>
            <p>2、计价标准：根据工程类别、工作成果、复杂程度等因素确定基础费率，并应用相应的调整系数。</p>
            <p>3、计算规则：咨询费用 = 基础费用 × 复杂系数 × 调整系数</p>
          </div>
          <p v-else>
            依据《广东省湛江市人民政府采购中心关于印发政府采购限价管理办法（试行）》（2020年12月），《广东省湛江市人民政府采购中心关于进一步规范政府采购活动的通知》（2019年5月）等文件，本项目采用{{ getCalculationMethodName(formData.calculationMethod) }}进行计算。通过分析历史采购数据、市场调研结果以及成本构成，综合考虑市场波动因素，得出合理的最高限价。
          </p>
        </div>
      </div>

      <!-- 四、各项限价 -->
      <div class="report-section">
        <h3 class="section-title">四、各项限价</h3>
        <div class="price-table">
          <table>
            <thead>
              <tr>
                <th>序号</th>
                <th>品目</th>
                <th>规格型号</th>
                <th>单位</th>
                <th>数量</th>
                <th>单价(元)</th>
                <th>金额(元)</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in tableItems" :key="index">
                <td>{{ index + 1 }}</td>
                <td>{{ item.name }}</td>
                <td>{{ item.spec }}</td>
                <td>{{ item.unit }}</td>
                <td>{{ item.quantity }}</td>
                <td>{{ formatPrice(item.unitPrice) }}</td>
                <td>{{ formatPrice(item.totalPrice) }}</td>
              </tr>
              <tr class="total-row">
                <td colspan="6">合计</td>
                <td>{{ formatPrice(calculatedPrice) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 五、合规性检查 -->
      <div class="report-section">
        <h3 class="section-title">五、合规性检查</h3>
        <div class="compliance-checks">
          <div class="check-item">
            <el-icon class="check-icon"><Check /></el-icon>
            <span>符合政府采购相关法规</span>
          </div>
          <div class="check-item">
            <el-icon class="check-icon"><Check /></el-icon>
            <span>符合行业价格指导标准</span>
          </div>
          <div class="check-item">
            <el-icon class="check-icon"><Check /></el-icon>
            <span>符合预算管理要求</span>
          </div>
          <div class="check-item">
            <el-icon class="check-icon"><Check /></el-icon>
            <span>符合市场公允价格原则</span>
          </div>
        </div>
      </div>

      <!-- 报告底部 -->
      <div class="report-footer">
        <div class="signature-area">
          <div class="signature-item">
            <div class="signature-label">编制人：</div>
            <div class="signature-value">{{ userInfo.real_name }}</div>
          </div>
          <div class="signature-item">
            <div class="signature-label">审核人：</div>
            <div class="signature-value">_____________</div>
          </div>
          <div class="signature-item">
            <div class="signature-label">日期：</div>
            <div class="signature-value">{{ currentDate }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="action-buttons">
      <el-button @click="prevStep" v-if="!isDetailMode">上一步</el-button>
      <!-- <el-button type="primary" @click="saveReport">保存报告</el-button> -->
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { Download, Printer, Share, Check } from '@element-plus/icons-vue';
import { saveProjectReport, checkReportExists, exportProjectReport, exportProjectReportPdf } from '@/api/xjzs/projectReport';

export default {
  name: 'GenerateReportStep',
  components: {
    Download,
    Printer,
    Share,
    Check
  },
  props: {
    formData: {
      type: Object,
      required: true
    },
    isDetailMode: { // 添加详情模式属性
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
  },
  emits: ['prev-step', 'finish'],
  setup(props, { emit }) {
    // 获取用户信息
    const currentUser =ref('LXBG');
    
    // 报告ID生成
    const reportId = ref('LXBG' + new Date().getFullYear() + String(Math.floor(Math.random() * 10000)).padStart(5, '0'));
    
    // 当前日期格式化
    const currentDate = computed(() => {
      const date = new Date();
      return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
    });
    
    // 计算得出的价格 - 从formData中获取
    const calculatedPrice = computed(() => {
      if (props.formData.calculationResult && props.formData.calculationResult.totalCost) {
        return parseFloat(props.formData.calculationResult.totalCost);
      }
      return 0;
    });
    
    // 价格中文大写
    const priceInWords = computed(() => {
      return convertToChinese(calculatedPrice.value);
    });
    
    // 表格项目 - 根据不同算法类型生成
    const tableItems = computed(() => {
      const items = [];
      if (props.formData.algorithmCategory === '培训类') {
        // 从培训类标准表中获取数据
        const trainingTableRows = props.formData.trainingTableRows || [];
        
        // 添加师资费
        trainingTableRows.forEach(row => {
          if (row.teacherTitle) {
            const hours = (row.trainingDays || 1) * 8; // 每天8小时
            let rate = 500; // 默认副高级
            
            if (row.teacherTitle.includes('正高级')) {
              rate = 1000;
            } else if (row.teacherTitle.includes('院士') || row.teacherTitle.includes('首席')) {
              rate = 1500;
            }
            
            items.push({
              name: `师资费-${row.teacherTitle}`,
              spec: '培训讲师',
              unit: '学时',
              quantity: hours,
              unitPrice: rate,
              totalPrice: hours * rate
            });
          }
        });
        
        // 如果是线下培训，添加综合定额费
        if (trainingTableRows.length > 0 && trainingTableRows[0].trainingLocation) {
          const days = trainingTableRows[0].trainingDays || 1;
          const people = trainingTableRows[0].trainingPeople || 0;
          
          if (people > 0) {
            items.push({
              name: '综合定额费',
              spec: '三类培训（处级及以下）',
              unit: '人天',
              quantity: people * days,
              unitPrice: 550,
              totalPrice: people * days * 550
            });
          }
        }
      } else if (props.formData.algorithmCategory === '工程咨询类') {
        // 从工程咨询类标准表中获取数据
        const engineeringTableRows = props.formData.engineeringTableRows || [];
        
        engineeringTableRows.forEach(row => {
          if (row.category && row.workResult) {
            items.push({
              name: `${row.category}-${row.workResult}`,
              spec: `复杂系数:${row.complexityFactor || '1.0'}`,
              unit: '项',
              quantity: 1,
              unitPrice: parseFloat(row.baseFee || 0),
              totalPrice: parseFloat(row.fee || 0)
            });
          }
        });
      } else {
        // 从标准表中获取数据
        const standardTableRows = props.formData.standardTableRows || [];
        
        standardTableRows.forEach(row => {
          if (row.feeName) {
            items.push({
              name: row.feeName,
              spec: row.specification || '-',
              unit: row.unit || '个',
              quantity: parseInt(row.quantity || 1),
              unitPrice: parseFloat(row.unitPrice || 0),
              totalPrice: parseFloat(row.totalPrice || 0)
            });
          }
        });
      }
      
      return items;
    });
    
    // 格式化价格
    const formatPrice = (price) => {
      return parseFloat(price).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };
    
    // 数字转中文大写
    const convertToChinese = (num) => {
      if (isNaN(num)) return '';
      
      const fraction = ['角', '分'];
      const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
      const unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟']
      ];
      
      let head = num < 0 ? '负' : '';
      num = Math.abs(num);
      
      let s = '';
      
      for (let i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(num * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
      }
      
      s = s || '整';
      num = Math.floor(num);
      
      for (let i = 0; i < unit[0].length && num > 0; i++) {
        let p = '';
        for (let j = 0; j < unit[1].length && num > 0; j++) {
          p = digit[num % 10] + unit[1][j] + p;
          num = Math.floor(num / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
      }
      
      return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
    };
    
    // 获取计算方式的中文名称
    const getCalculationMethodName = (method) => {
      const methodNames = {
        'government': '政府、行业指导价格法',
        'cost': '成本核算法',
        'historical': '历史价格法',
        'market': '市场调研法',
        'comprehensive': '综合定价法'
      };
      return methodNames[method] || '综合定价法';
    };
    
    
    // 导出PDF报告
    // 导出PDF报告
    const downloadReportPdf = async () => {
    // 创建加载实例
    const loading = ElLoading.service({
    lock: true,
    text: '报告导出中，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
    });
    
    try {
    // 检查是否有项目ID
    if (!props.formData.projectId && !props.formData.id) {
    ElMessage.error('缺少项目ID，无法下载报告');
    loading.close(); // 关闭加载动画
    return;
    }
    
    // 使用projectId或id作为项目ID
    const projectId = props.formData.projectId || props.formData.id;
    // 当报告不存在时，需要先保存报告
    const checkResult = await checkReportExists(projectId);
    if (!checkResult.data && !checkResult.data.exists) {
    await saveReport(projectId);
    }
    
    // 更优雅的写法：直接在声明时确定值
    const tabledata = props.formData.algorithmCategory === '培训类' 
    ? props.formData.trainingTableRows
    : props.formData.algorithmCategory === '工程咨询类'
    ? props.formData.engineeringTableRows
    : props.formData.jdTableRows;
    
    // 调用导出PDF API
    const response = await exportProjectReportPdf(projectId, JSON.stringify(tableItems.value), JSON.stringify(tabledata));
    
    // 创建Blob对象
    const blob = new Blob([response.data], { type: 'application/docx' });
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1并补零
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;
    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${props.formData.projectName}最高限价计算报告_${timestamp}.docx`;
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    
    ElMessage.success('报告导出成功');
    } catch (error) {
    ElMessage.error('报告导出失败：' + (error.message || '未知错误'));
    } finally {
    // 无论成功还是失败，都关闭加载动画
    loading.close();
    }
    };
    
    // 打印报告
    const printReport = () => {
      window.print();
    };
    
    // 分享报告
    const shareReport = () => {
      ElMessage.success('报告分享链接已复制到剪贴板');
      // 实际项目中这里应该调用分享API
    };
    
    const prevStep = () => {
      emit('prev-step');
    };
    
    const finish = () => {
      emit('finish');
    };
    
    // 保存报告
    const saveReport = async () => {
      try {
        // 检查是否有项目ID
        if (!props.formData.projectId && !props.formData.id) {
          ElMessage.error('缺少项目ID，无法保存报告');
          return;
        }
        
        // 使用projectId或id作为项目ID
        const projectId = props.formData.projectId || props.formData.id;
        
        // 检查是否已存在报告
        const checkResult = await checkReportExists(projectId);
        
        if (checkResult.data && checkResult.data.exists) {
          // 如果已存在报告，提示用户
          ElMessageBox.confirm(
            '该项目已存在报告记录，保存将覆盖原有记录，是否继续？',
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          ).then(async () => {
            await doSaveReport(projectId);
          }).catch(() => {
            ElMessage.info('已取消保存');
          });
        } else {
          // 不存在报告，直接保存
          await doSaveReport(projectId);
        }
      } catch (error) {
        ElMessage.error('检查报告记录失败：' + (error.message || '未知错误'));
      }
    };
    
    // 执行保存报告
    const doSaveReport = async (projectId) => {
      try {
        const reportData = {
          projectId: projectId,
          projectName: props.formData.projectName,
          projectCode: props.formData.projectCode,
          reportId: reportId.value,
          calculationMethod: props.formData.calculationMethod,
          algorithmCategory: props.formData.algorithmCategory,
          totalPrice: calculatedPrice.value,
          reportContent: JSON.stringify({
            formData: props.formData,
            tableItems: tableItems.value,
            calculatedPrice: calculatedPrice.value,
            priceInWords: priceInWords.value,
            reportId: reportId.value,
            createTime: new Date().toISOString(),
            creator: currentUser.value
          }),
          // createUser: userInfo.userId,
          // createDept: userInfo.deptId
        };
        
        const result = await saveProjectReport(reportData);
        
        if (result.data && result.data.success) {
          ElMessage.success('报告保存成功');
        } else {
          ElMessage.error('报告保存失败：' + (result.data?.message || '未知错误'));
        }
      } catch (error) {
        ElMessage.error('保存报告失败：' + (error.message || '未知错误'));
      }
    };
    
    // 组件挂载时检查计算结果和报告数据
    onMounted(async () => {
      if (!props.formData.calculationResult && !props.isDetailMode) {
        console.warn('未找到计算结果，请先完成计算步骤');
      }
      
      // 如果是详情模式，并且有报告数据，则使用报告数据
      if (props.isDetailMode && props.formData.reportData) {
        // 使用报告数据中的报告ID
        reportId.value = props.formData.reportData.reportId || reportId.value;
        
        // 如果报告内容中包含更多数据，也可以使用
        if (props.formData.reportData.reportContent) {
          try {
            const reportContent = JSON.parse(props.formData.reportData.reportContent);
            // 可以使用报告内容中的其他数据
            if (reportContent.calculatedPrice) {
              // 这里不需要直接设置计算价格，因为它是计算属性
              // 但可以确保formData中有正确的计算结果
              if (!props.formData.calculationResult) {
                props.formData.calculationResult = {
                  totalCost: reportContent.calculatedPrice
                };
              }
            }
          } catch (error) {
            console.error('解析报告内容失败:', error);
          }
        }
      }
    });
    
    return {
      reportId,
      currentDate,
      currentUser,
      calculatedPrice,
      priceInWords,
      tableItems,
      formatPrice,
      getCalculationMethodName,
      saveReport,
      downloadReportPdf,
      printReport,
      shareReport,
      prevStep,
      finish
    };
  }
}
</script>

<style scoped>
.step-content {
  padding: 10px;
}

.step-content h3 {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.step-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 25px;
}

.report-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.report-container {
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.report-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #409EFF;
}

.report-title {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
}

.report-meta {
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
}

.report-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
  border-bottom: 1px dashed #e4e7ed;
  padding-bottom: 8px;
}

.info-label {
  width: 100px;
  color: #606266;
  font-weight: 500;
}

.info-value {
  flex: 1;
  color: #303133;
}

.price-summary {
  background-color: #f0f9eb;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
}

.price-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 10px;
}

.price-value {
  font-size: 28px;
  font-weight: bold;
  color: #67c23a;
  margin-bottom: 5px;
}

.price-in-words {
  color: #606266;
  font-size: 14px;
}

.section-content {
  color: #606266;
  line-height: 1.6;
}

.price-table {
  width: 100%;
  overflow-x: auto;
}

.price-table table {
  width: 100%;
  border-collapse: collapse;
}

.price-table th, .price-table td {
  border: 1px solid #e4e7ed;
  padding: 12px 8px;
  text-align: center;
}

.price-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

.price-table .total-row {
  background-color: #f0f9eb;
  font-weight: bold;
}

.compliance-checks {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.check-item {
  display: flex;
  align-items: center;
  color: #67c23a;
}

.check-icon {
  margin-right: 8px;
}

.report-footer {
  margin-top: 50px;
  display: flex;
  justify-content: space-between;
}

.signature-area {
  display: flex;
  gap: 50px;
}

.signature-item {
  display: flex;
  align-items: center;
}

.signature-label {
  margin-right: 10px;
  color: #606266;
}

.report-seal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
  border: 2px solid #f56c6c;
  border-radius: 50%;
  color: #f56c6c;
  position: relative;
  transform: rotate(-15deg);
}

.seal-text {
  font-weight: bold;
  font-size: 14px;
}

.seal-date {
  font-size: 12px;
  margin-top: 5px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.action-buttons .el-button {
  min-width: 100px;
}

@media print {
  .report-actions, .action-buttons {
    display: none;
  }
  
  .report-container {
    border: none;
    box-shadow: none;
    padding: 0;
  }
}
</style>

















